<?php
set_time_limit(0);
$ip = '0.0.0.0'; // 监听所有可用接口
$port = 52669;   // 指定端口
$time1 = date("Y-m-d H:i:s");
require_once('db.php');
$dbo = new proPdo();
// 创建 socket
if (($sock = socket_create(AF_INET, SOCK_STREAM, SOL_TCP)) === false) {
    echo "socket_create() 失败: " . socket_strerror(socket_last_error()) . "\n";
    exit;
}

// 绑定 socket 到地址和端口
if (socket_bind($sock, $ip, $port) === false) {
    echo "socket_bind() 失败: " . socket_strerror(socket_last_error($sock)) . "\n";
    socket_close($sock);
    exit;
}

// 开始监听连接
if (socket_listen($sock, 4) === false) {
    echo "socket_listen() 失败: " . socket_strerror(socket_last_error($sock)) . "\n";
    socket_close($sock);
    exit;
}

echo "服务器正在监听端口 $port...\n";

do {
    // 接受一个客户端连接
    if (($msgsock = socket_accept($sock)) === false) {
        echo "socket_accept() 失败: " . socket_strerror(socket_last_error($sock)) . "\n";
        break;
    } else {
        echo "通信连接成功\n";
        
        // 创建子进程处理客户端连接
        $pid = pcntl_fork();
        
        if ($pid == -1) {
            // 处理错误
            echo "无法创建子进程\n";
            socket_close($msgsock);
        } elseif ($pid) {
            // 父进程：关闭客户端 socket，继续接受新的连接
            socket_close($msgsock);
        } else {
            // 子进程：处理客户端请求
            handleClient($msgsock);
            exit(0); // 子进程完成后退出
        }
    }
} while (true); // 这个循环实际上会使服务器持续运行，接受新的连接

function handleClient($msgsock) {
    $dbo = new proPdo();
    $count = 0; // 添加计数器用于跟踪接收次数
    $time1 = date("Y-m-d H:i:s");
    $threadId = getmypid(); // 获取当前线程的进程ID
    $timeout = 10; // 设置超时时间为10秒
    $lastActivity = time(); // 记录最后活动时间

    // 发送首次连接响应
    $msg = '{"time":"' . $time1 . '"}';
    $string = hex2bin('0000045A00000020') . $msg . hex2bin('0D0A');
    if (socket_write($msgsock, $string, strlen($string)) === false) {
        echo "socket_write() 失败: " . socket_strerror(socket_last_error($msgsock)) . "\n";
    } 
    echo "线程 $threadId 发送给客户端的数据: $msg\n"; // 打印发送给客户端的数据

    // 添加内部循环处理与当前客户端的持续通信
    while (true) {
        echo "线程 $threadId 等待读取客户端数据...\n";
        $count++; // 每次接收数据时增加计数器

        // 检查socket是否有可读数据
        $read = array($msgsock);
        $write = null;
        $except = null;
        $tv_sec = 5; // 5秒超时
        
        if (socket_select($read, $write, $except, $tv_sec) > 0) {
            $buf = '';
            $input = socket_read($msgsock, 2048, PHP_BINARY_READ);
            
            // 检查连接是否已关闭
            if ($input === false || strlen($input) === 0) {
                echo "线程 $threadId 客户端断开连接\n";
                socket_close($msgsock);
                break;
            }
            
            $buf .= $input;
            $替换字符串 = bin2hex($buf);
            $替换字符串 = substr($替换字符串, 0, 136 * 2);
            $newString = substr($替换字符串, 16);
            $替换字符1串 = ttt($newString);
            $替换字符2串 = aes_cbc_128_decrypt($替换字符1串);
            echo "线程 $threadId 解密: " . $替换字符2串 . "\n";
            $xq = json_decode($替换字符2串, true);
            $code = $xq['code'];
            $udid = $xq['seid'];
            $timestamp = time();
            $todos     = $dbo->doSql("select * from `t_code` where `auth_code` = '{$code}' order by `id` desc");
            $todos     = $todos[0];
            $timee      = $todos['expiry_date'];
            $type      = $todos['code_type'];
            
            // 更新最后活动时间
            $lastActivity = time();

            if ($count == 1) {
                if (!$todos) {
                    $msg = '{"code":2002}';
                }
                if ($todos['status'] == 1 && $todos['uuid'] != $udid && $udid) {
        
                	$msg = '{"code":2002}';
                }
                if ($todos['is_delete'] == 1) {
                
                	$msg = '{"code":2002}';
                }
                if ($todos['status'] == 0) {
                    $endtime = date("Y-m-d H:i:s",strtotime('+1 '.$type));
                    
                    if(empty($todos['user_time'])){
                        $dbo->execute("update `t_code` set `user_time`=now(),`expiry_date`='{$endtime}',`reg_time`='{$timestamp}',`status`=1,`uuid`='{$udid}' where `auth_code`='{$code}'");
                    }else{
                    	$dbo->execute("update `t_code` set `status`=1,`uuid`='{$udid}' where `auth_code`='{$code}'");
                    }
                   $msg = '{"code":"2000","et":'.strtotime($endtime).',"ts":"'.time().'"}';

                }
                if ($todos['status'] == 1 && strtotime($todos['expiry_date']) > time()) {
                    $msg = '{"code":"2000","et":'.strtotime($timee).',"ts":"'.time().'"}';
                	
                } else {
                    $msg = '{"code":2002}';
                }
                 
                  echo "线程 $threadId 返回给客户端的数据: $msg\n"; // 打印返回给客户端的数据
                 
                $msg = aes_cbc_128_encrypt($msg);
                $msg = t2($msg);
                $string = hex2bin('000007D000000058').$msg;
                if (socket_write($msgsock, $string, strlen($string)) === false) {
                    echo "socket_write() 失败: " . socket_strerror(socket_last_error($msgsock)) . "\n";
                    socket_close($msgsock);
                    break;
                }
                
                
                
                
                
                
                
            } elseif ($count == 2) {
                // 第二次接收：只发送响应3
                 if (!$todos) {
                    $msg = '000007D0000000587175726253346C30612138236153564C752B657A54777E7E';
                }
                if ($todos['status'] == 1 && $todos['uuid'] != $udid && $udid) {
        
                	$msg = '000007D0000000587175726253346C30612138236153564C752B657A54777E7E';
                }
                if ($todos['is_delete'] == 1) {
                
                	$msg = '000007D0000000587175726253346C30612138236153564C752B657A54777E7E';
                }
                if ($todos['status'] == 1 && strtotime($todos['expiry_date']) > time()) {
                    $msg = '00001495000000EC584E4D68786772547639746555645171304674725654324053704E77404F534C596D716A384479646D6F455732334E5478614547407347622133564B7074716B65446738336C6E496D4C4A79796E763256626D6D58464B6C675949646A5771526F4636452D445276362D66456361366C7A4654483430507A5164396C4037767632366A7A4877736B5A4B64453951766A31674F5639457A644F6A54454550624D794D6249495A79504C316579666B793149707779723550723375364C3778446B4A2D78732350446E4D63404F556175493649716345577773305271384F56664A386F6F4C34534C486A75387E';
                	
                } else {
                    $msg = '000007D0000000587175726253346C30612138236153564C752B657A54777E7E';
                }
                
                echo "线程 $threadId 返回给客户端的数据: $msg\n"; // 打印返回给客户端的数据
                $string = hex2bin($msg);
                if (socket_write($msgsock, $string, strlen($string)) === false) {
                    echo "socket_write() 失败: " . socket_strerror(socket_last_error($msgsock)) . "\n";
                }
                
                
                
                
            } else {
                
                
               if (!$todos) {
                    $msg = '{"code":2002}';
                }
                if ($todos['status'] == 1 && $todos['uuid'] != $udid && $udid) {
        
                	$msg = '{"code":2002}';
                }
                if ($todos['is_delete'] == 1) {
                
                	$msg = '{"code":2002}';
                }
                if ($todos['status'] == 1 && strtotime($todos['expiry_date']) > time()) {
                    $msg = '{"code":"2000","et":'.strtotime($timee).',"ts":"'.time().'"}';
                	
                } else {
                    $msg = '{"code":2002}';
                }
                
                echo "线程 $threadId 返回给客户端的数据: $msg\n"; // 打印返回给客户端的数据
                $cycle = ($count - 3) % 3;
                // $msg = '{"code":"2000","et":1770992039,"ts":"'.time().'"}';
                $msg = aes_cbc_128_encrypt($msg);
                $msg = t2($msg);
                $string = hex2bin('000007D000000058').$msg;
                if (socket_write($msgsock, $string, strlen($string)) === false) {
                    echo "socket_write() 失败: " . socket_strerror(socket_last_error($msgsock)) . "\n";
                    socket_close($msgsock);
                    break;
                }
                if ($cycle !== 2) {
                    $msg = '{"time":"' . $time1 . '"}';
                    $string = hex2bin('0000045A00000020') . $msg . hex2bin('0D0A');
                    if (socket_write($msgsock, $string, strlen($string)) === false) {
                        echo "socket_write() 失败: " . socket_strerror(socket_last_error($msgsock)) . "\n";
                    }
                }
                
                
                
            }

            
        } else {
            echo "线程 $threadId 等待客户端数据超时\n";
      
        }

        // 检查是否超时
        if (time() - $lastActivity > $timeout) {
            echo "线程 $threadId 超时，关闭连接\n";
            socket_close($msgsock);
            break;
        }
    }
}

function ttt($newString) {
   $newString = hex2bin($newString);
   $newString = str_replace('_', '+', $newString);
   $newString = str_replace('!', 'A', $newString);
   $newString = str_replace('#', 'C', $newString);
   $newString = str_replace('-', '/', $newString);
   $newString = str_replace('@', 'B', $newString);
   return $newString;
}
function t2($newString) {
   $newString = str_replace('B', '@', $newString);
   $newString = str_replace('A', '!', $newString);
    $newString = str_replace('/', '-', $newString);
    $newString = str_replace('C', '#', $newString);
   $newString = str_replace('=', '~', $newString);
   return $newString;
}
function aes_cbc_128_encrypt($plaintext) {
    $key = 'p4m4gjk59o7nuedr';
    $iv = 'cf37z0j92mozpqft';
    $ciphertext = openssl_encrypt($plaintext, 'aes-128-cbc', $key, OPENSSL_RAW_DATA, $iv);
    $encodedCiphertext = base64_encode($ciphertext);
    return $encodedCiphertext;
}
 
function aes_cbc_128_decrypt($encodedCiphertext) {
    $key = 'p4m4gjk59o7nuedr';
    $iv = 'cf37z0j92mozpqft';
    $ciphertext = base64_decode($encodedCiphertext);
    $plaintext = openssl_decrypt($ciphertext, 'aes-128-cbc', $key, OPENSSL_RAW_DATA, $iv);
    return $plaintext;
}



// 关闭主 socket
socket_close($sock);

?>