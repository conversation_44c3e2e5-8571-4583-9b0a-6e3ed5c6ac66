# PHP与Go时间格式对应关系

## 时间格式对比

### PHP时间格式
```php
date("Y-m-d H:i:s")  // 输出: 2024-01-15 14:30:25
```

### Go时间格式  
```go
time.Now().Format("2006-01-02 15:04:05")  // 输出: 2024-01-15 14:30:25
```

## Go时间格式模板说明

Go语言使用固定的参考时间：`Mon Jan 2 15:04:05 MST 2006`

对应的数字格式：
- `2006` = 年份 (对应PHP的Y)
- `01` = 月份 (对应PHP的m) 
- `02` = 日期 (对应PHP的d)
- `15` = 小时24小时制 (对应PHP的H)
- `04` = 分钟 (对应PHP的i)
- `05` = 秒 (对应PHP的s)

## 代码中的时间处理对应关系

### 1. 当前时间获取
**PHP:**
```php
$time1 = date("Y-m-d H:i:s");
```

**Go:**
```go
time1 := time.Now().Format("2006-01-02 15:04:05")
```

### 2. 时间戳获取
**PHP:**
```php
$timestamp = time();
```

**Go:**
```go
timestamp := time.Now().Unix()
```

### 3. 过期时间计算
**PHP:**
```php
$endtime = date("Y-m-d H:i:s", strtotime('+1 '.$type));
```

**Go:**
```go
var endTime time.Time
switch codeType {
case "day":
    endTime = time.Now().AddDate(0, 0, 1)  // +1天
case "month": 
    endTime = time.Now().AddDate(0, 1, 0)  // +1月
case "year":
    endTime = time.Now().AddDate(1, 0, 0)  // +1年
}
endTimeStr := endTime.Format("2006-01-02 15:04:05")
```

### 4. 时间比较
**PHP:**
```php
if (strtotime($todos['expiry_date']) > time()) {
    // 未过期
}
```

**Go:**
```go
expiryTime, _ := time.Parse("2006-01-02 15:04:05", authInfo.ExpiryDate)
if expiryTime.After(time.Now()) {
    // 未过期
}
```

### 5. 时间戳转换
**PHP:**
```php
$et = strtotime($endtime);  // 字符串时间转时间戳
```

**Go:**
```go
et := expiryTime.Unix()  // Time对象转时间戳
```

## 为什么Go使用这种格式？

Go的设计者选择了一个容易记忆的参考时间：
- 数字顺序：1 2 3 4 5 6 7
- 对应：Mon(1) Jan(2) 3 4 5 6 7
- 完整时间：Mon Jan 2 15:04:05 MST 2006

这样设计的好处是：
1. 容易记忆数字顺序
2. 避免了传统格式字符串的混淆
3. 类型安全，编译时检查格式正确性

## 实际输出示例

两种语言的输出完全一致：
```
2024-01-15 14:30:25
```
