package main

import (
	"crypto/aes"
	"crypto/cipher"
	"database/sql"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"log"
	"net"
	"strings"
	"sync"
	"time"

	_ "github.com/go-sql-driver/mysql"
)

// 数据库配置
const (
	DBHost     = "127.0.0.1"
	DBPort     = "3306"
	DBName     = "qqqq"
	DBUser     = "qqqq"
	DBPassword = "qqqq"
	ServerPort = ":52669"
)

// AES加密配置
const (
	AESKey = "p4m4gjk59o7nuedr"
	AESIV  = "cf37z0j92mozpqft"
)

// 数据库连接池
var db *sql.DB
var dbMutex sync.RWMutex

// 授权码信息结构
type AuthCode struct {
	ID         string `json:"id"`         // 改为string类型，因为数据库中存储的是UUID
	AuthCode   string `json:"auth_code"`
	Status     int    `json:"status"`
	UUID       string `json:"uuid"`
	IsDelete   int    `json:"is_delete"`
	ExpiryDate string `json:"expiry_date"`
	UserTime   string `json:"user_time"`
	RegTime    string `json:"reg_time"`
	CodeType   string `json:"code_type"`
}

// 客户端请求结构
type ClientRequest struct {
	Code string `json:"code"`
	Seid string `json:"seid"`
}

// 服务器响应结构
type ServerResponse struct {
	Code string `json:"code"`
	Et   int64  `json:"et,omitempty"`
	Ts   string `json:"ts,omitempty"`
}

// 时间响应结构
type TimeResponse struct {
	Time string `json:"time"`
}

func main() {
	// 初始化数据库连接
	if err := initDB(); err != nil {
		log.Fatal("数据库连接失败:", err)
	}
	defer db.Close()

	// 创建TCP监听器
	listener, err := net.Listen("tcp", ServerPort)
	if err != nil {
		log.Fatal("监听端口失败:", err)
	}
	defer listener.Close()

	fmt.Printf("服务器正在监听端口 %s...\n", ServerPort)

	// 接受客户端连接
	for {
		conn, err := listener.Accept()
		if err != nil {
			log.Printf("接受连接失败: %v", err)
			continue
		}

		// 为每个客户端启动一个goroutine
		go handleClient(conn)
	}
}

// 初始化数据库连接
func initDB() error {
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		DBUser, DBPassword, DBHost, DBPort, DBName)

	var err error
	db, err = sql.Open("mysql", dsn)
	if err != nil {
		return err
	}

	// 设置连接池参数
	db.SetMaxOpenConns(100)
	db.SetMaxIdleConns(10)
	db.SetConnMaxLifetime(time.Hour)

	// 测试连接
	if err = db.Ping(); err != nil {
		return err
	}

	return nil
}

// 处理客户端连接
func handleClient(conn net.Conn) {
	defer conn.Close()

	clientAddr := conn.RemoteAddr().String()
	fmt.Printf("客户端 %s 连接成功\n", clientAddr)

	count := 0
	timeout := 10 * time.Second
	
	// 发送首次连接响应 (对应PHP: date("Y-m-d H:i:s"))
	timeResp := TimeResponse{Time: time.Now().Format("2006-01-02 15:04:05")}
	timeMsg, _ := json.Marshal(timeResp)
	
	firstResponse := append(hexToBytes("0000045A00000020"), timeMsg...)
	firstResponse = append(firstResponse, hexToBytes("0D0A")...)
	
	if _, err := conn.Write(firstResponse); err != nil {
		log.Printf("客户端 %s 发送首次响应失败: %v", clientAddr, err)
		return
	}
	fmt.Printf("客户端 %s 发送数据: %s\n", clientAddr, string(timeMsg))

	// 设置读取超时
	conn.SetReadDeadline(time.Now().Add(timeout))

	// 持续处理客户端请求
	for {
		buffer := make([]byte, 2048)
		n, err := conn.Read(buffer)
		if err != nil {
			if netErr, ok := err.(net.Error); ok && netErr.Timeout() {
				fmt.Printf("客户端 %s 读取超时\n", clientAddr)
			} else {
				fmt.Printf("客户端 %s 断开连接: %v\n", clientAddr, err)
			}
			break
		}

		if n == 0 {
			fmt.Printf("客户端 %s 断开连接\n", clientAddr)
			break
		}

		count++
		fmt.Printf("客户端 %s 第 %d 次接收数据\n", clientAddr, count)

		// 处理接收到的数据
		data := buffer[:n]
		if err := processClientData(conn, data, count, clientAddr); err != nil {
			log.Printf("客户端 %s 处理数据失败: %v", clientAddr, err)
			break
		}

		// 重置超时
		conn.SetReadDeadline(time.Now().Add(timeout))
	}
}

// 处理客户端数据
func processClientData(conn net.Conn, data []byte, count int, clientAddr string) error {
	// 转换为十六进制字符串
	hexString := hex.EncodeToString(data)
	
	// 截取固定长度并去掉头部
	if len(hexString) < 16 {
		return fmt.Errorf("数据长度不足")
	}
	
	maxLen := 136 * 2
	if len(hexString) > maxLen {
		hexString = hexString[:maxLen]
	}
	
	newString := hexString[16:]
	
	// 解码和解密
	decodedString := decodeString(newString)
	decryptedData := aesDecrypt(decodedString)
	
	fmt.Printf("客户端 %s 解密数据: %s\n", clientAddr, decryptedData)
	
	// 解析JSON请求
	var clientReq ClientRequest
	if err := json.Unmarshal([]byte(decryptedData), &clientReq); err != nil {
		return fmt.Errorf("解析JSON失败: %v", err)
	}

	// 查询数据库获取授权信息
	authInfo, err := getAuthCode(clientReq.Code)
	if err != nil {
		return fmt.Errorf("查询数据库失败: %v", err)
	}

	// 根据不同的通信次数处理响应
	return handleResponse(conn, authInfo, clientReq, count, clientAddr)
}

// 查询授权码信息
func getAuthCode(code string) (*AuthCode, error) {
	dbMutex.RLock()
	defer dbMutex.RUnlock()

	query := "SELECT id, auth_code, status, uuid, is_delete, expiry_date, user_time, reg_time, code_type FROM t_code WHERE auth_code = ? ORDER BY id DESC LIMIT 1"

	var authCode AuthCode
	var expiryDate, userTime, regTime sql.NullString

	err := db.QueryRow(query, code).Scan(
		&authCode.ID,
		&authCode.AuthCode,
		&authCode.Status,
		&authCode.UUID,
		&authCode.IsDelete,
		&expiryDate,
		&userTime,
		&regTime,
		&authCode.CodeType,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil // 未找到记录
		}
		return nil, err
	}

	// 处理可能为NULL的字段
	if expiryDate.Valid {
		authCode.ExpiryDate = expiryDate.String
	}
	if userTime.Valid {
		authCode.UserTime = userTime.String
	}
	if regTime.Valid {
		authCode.RegTime = regTime.String
	}

	return &authCode, nil
}

// 更新授权码状态
func updateAuthCode(code, udid, codeType string) error {
	dbMutex.Lock()
	defer dbMutex.Unlock()

	// 计算过期时间 (对应PHP: strtotime('+1 '.$type))
	var endTime time.Time
	switch codeType {
	case "day", "days":
		endTime = time.Now().AddDate(0, 0, 1)  // +1 day
	case "month", "months":
		endTime = time.Now().AddDate(0, 1, 0)  // +1 month
	case "year", "years":
		endTime = time.Now().AddDate(1, 0, 0)  // +1 year
	default:
		endTime = time.Now().AddDate(0, 0, 1)  // 默认1天
	}

	// 检查是否已有用户时间
	var userTime sql.NullString
	checkQuery := "SELECT user_time FROM t_code WHERE auth_code = ?"
	err := db.QueryRow(checkQuery, code).Scan(&userTime)
	if err != nil {
		return err
	}

	var updateQuery string
	var args []interface{}

	if !userTime.Valid || userTime.String == "" {
		// 首次使用，更新所有字段 (对应PHP: date("Y-m-d H:i:s", strtotime))
		updateQuery = "UPDATE t_code SET user_time = NOW(), expiry_date = ?, reg_time = ?, status = 1, uuid = ? WHERE auth_code = ?"
		args = []interface{}{endTime.Format("2006-01-02 15:04:05"), time.Now().Unix(), udid, code}
	} else {
		// 已有用户时间，只更新状态和UUID
		updateQuery = "UPDATE t_code SET status = 1, uuid = ? WHERE auth_code = ?"
		args = []interface{}{udid, code}
	}

	_, err = db.Exec(updateQuery, args...)
	return err
}

// 处理响应逻辑
func handleResponse(conn net.Conn, authInfo *AuthCode, clientReq ClientRequest, count int, clientAddr string) error {
	var responseMsg string
	var err error

	// 验证授权码
	if authInfo == nil {
		responseMsg = `{"code":"2002"}`
	} else if authInfo.IsDelete == 1 {
		responseMsg = `{"code":"2002"}`
	} else if authInfo.Status == 1 && authInfo.UUID != clientReq.Seid && clientReq.Seid != "" {
		responseMsg = `{"code":"2002"}`
	} else if authInfo.Status == 0 {
		// 首次激活
		if err := updateAuthCode(clientReq.Code, clientReq.Seid, authInfo.CodeType); err != nil {
			return fmt.Errorf("更新授权码失败: %v", err)
		}

		// 重新查询获取更新后的信息
		authInfo, err = getAuthCode(clientReq.Code)
		if err != nil {
			return err
		}

		if authInfo != nil {
			expiryTime, _ := time.Parse("2006-01-02 15:04:05", authInfo.ExpiryDate)
			responseMsg = fmt.Sprintf(`{"code":"2000","et":%d,"ts":"%d"}`,
				expiryTime.Unix(), time.Now().Unix())
		} else {
			responseMsg = `{"code":"2002"}`
		}
	} else if authInfo.Status == 1 {
		// 检查是否过期 (对应PHP: strtotime($todos['expiry_date']) > time())
		expiryTime, err := time.Parse("2006-01-02 15:04:05", authInfo.ExpiryDate)
		if err != nil || expiryTime.Before(time.Now()) {
			responseMsg = `{"code":"2002"}`
		} else {
			responseMsg = fmt.Sprintf(`{"code":"2000","et":%d,"ts":"%d"}`,
				expiryTime.Unix(), time.Now().Unix())
		}
	} else {
		responseMsg = `{"code":"2002"}`
	}

	fmt.Printf("客户端 %s 返回数据: %s\n", clientAddr, responseMsg)

	// 根据通信次数发送不同格式的响应
	switch count {
	case 1:
		return sendFirstResponse(conn, responseMsg, authInfo, clientReq)
	case 2:
		return sendSecondResponse(conn, responseMsg, authInfo, clientReq)
	default:
		return sendSubsequentResponse(conn, responseMsg, count, clientAddr)
	}
}

// 发送第一次响应
func sendFirstResponse(conn net.Conn, responseMsg string, authInfo *AuthCode, clientReq ClientRequest) error {
	// 加密响应消息
	encryptedMsg := aesEncrypt(responseMsg)
	encodedMsg := encodeString(encryptedMsg)

	// 构造响应数据
	response := append(hexToBytes("000007D000000058"), []byte(encodedMsg)...)

	_, err := conn.Write(response)
	return err
}

// 发送第二次响应
func sendSecondResponse(conn net.Conn, responseMsg string, authInfo *AuthCode, clientReq ClientRequest) error {
	var hexResponse string

	// 根据验证结果返回不同的硬编码响应
	if authInfo == nil || authInfo.IsDelete == 1 ||
		(authInfo.Status == 1 && authInfo.UUID != clientReq.Seid && clientReq.Seid != "") {
		hexResponse = "000007D0000000587175726253346C30612138236153564C752B657A54777E7E"
	} else if authInfo.Status == 1 {
		expiryTime, err := time.Parse("2006-01-02 15:04:05", authInfo.ExpiryDate)
		if err != nil || expiryTime.Before(time.Now()) {
			hexResponse = "000007D0000000587175726253346C30612138236153564C752B657A54777E7E"
		} else {
			hexResponse = "00001495000000EC584E4D68786772547639746555645171304674725654324053704E77404F534C596D716A384479646D6F455732334E5478614547407347622133564B7074716B65446738336C6E496D4C4A79796E763256626D6D58464B6C675949646A5771526F4636452D445276362D66456361366C7A4654483430507A5164396C4037767632366A7A4877736B5A4B64453951766A31674F5639457A644F6A54454550624D794D6249495A79504C316579666B793149707779723550723375364C3778446B4A2D78732350446E4D63404F556175493649716345577773305271384F56664A386F6F4C34534C486A75387E"
		}
	} else {
		hexResponse = "000007D0000000587175726253346C30612138236153564C752B657A54777E7E"
	}

	response := hexToBytes(hexResponse)
	_, err := conn.Write(response)
	return err
}

// 发送后续响应
func sendSubsequentResponse(conn net.Conn, responseMsg string, count int, clientAddr string) error {
	// 加密响应消息
	encryptedMsg := aesEncrypt(responseMsg)
	encodedMsg := encodeString(encryptedMsg)

	// 构造响应数据
	response := append(hexToBytes("000007D000000058"), []byte(encodedMsg)...)

	if _, err := conn.Write(response); err != nil {
		return err
	}

	// 每3次循环发送时间戳（除了第3、6、9...次）
	cycle := (count - 3) % 3
	if cycle != 2 {
		// 发送时间戳响应 (对应PHP: date("Y-m-d H:i:s"))
		timeResp := TimeResponse{Time: time.Now().Format("2006-01-02 15:04:05")}
		timeMsg, _ := json.Marshal(timeResp)

		timeResponse := append(hexToBytes("0000045A00000020"), timeMsg...)
		timeResponse = append(timeResponse, hexToBytes("0D0A")...)

		_, err := conn.Write(timeResponse)
		return err
	}

	return nil
}

// 字符串解码（对应PHP的ttt函数）
func decodeString(hexString string) string {
	data, err := hex.DecodeString(hexString)
	if err != nil {
		return ""
	}

	str := string(data)
	str = strings.ReplaceAll(str, "_", "+")
	str = strings.ReplaceAll(str, "!", "A")
	str = strings.ReplaceAll(str, "#", "C")
	str = strings.ReplaceAll(str, "-", "/")
	str = strings.ReplaceAll(str, "@", "B")

	return str
}

// 字符串编码（对应PHP的t2函数）
func encodeString(str string) string {
	str = strings.ReplaceAll(str, "B", "@")
	str = strings.ReplaceAll(str, "A", "!")
	str = strings.ReplaceAll(str, "/", "-")
	str = strings.ReplaceAll(str, "C", "#")
	str = strings.ReplaceAll(str, "=", "~")

	return str
}

// AES加密
func aesEncrypt(plaintext string) string {
	key := []byte(AESKey)
	iv := []byte(AESIV)

	block, err := aes.NewCipher(key)
	if err != nil {
		log.Printf("AES加密失败: %v", err)
		return ""
	}

	// PKCS7填充
	padding := aes.BlockSize - len(plaintext)%aes.BlockSize
	padtext := plaintext + strings.Repeat(string(rune(padding)), padding)

	ciphertext := make([]byte, len(padtext))
	mode := cipher.NewCBCEncrypter(block, iv)
	mode.CryptBlocks(ciphertext, []byte(padtext))

	return base64.StdEncoding.EncodeToString(ciphertext)
}

// AES解密
func aesDecrypt(encodedCiphertext string) string {
	key := []byte(AESKey)
	iv := []byte(AESIV)

	ciphertext, err := base64.StdEncoding.DecodeString(encodedCiphertext)
	if err != nil {
		log.Printf("Base64解码失败: %v", err)
		return ""
	}

	block, err := aes.NewCipher(key)
	if err != nil {
		log.Printf("AES解密失败: %v", err)
		return ""
	}

	if len(ciphertext)%aes.BlockSize != 0 {
		log.Printf("密文长度不正确")
		return ""
	}

	plaintext := make([]byte, len(ciphertext))
	mode := cipher.NewCBCDecrypter(block, iv)
	mode.CryptBlocks(plaintext, ciphertext)

	// 去除PKCS7填充
	if len(plaintext) > 0 {
		padding := int(plaintext[len(plaintext)-1])
		if padding > 0 && padding <= aes.BlockSize {
			plaintext = plaintext[:len(plaintext)-padding]
		}
	}

	return string(plaintext)
}

// 十六进制字符串转字节数组
func hexToBytes(hexStr string) []byte {
	bytes, err := hex.DecodeString(hexStr)
	if err != nil {
		log.Printf("十六进制解码失败: %v", err)
		return nil
	}
	return bytes
}
