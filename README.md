# Go授权验证服务器

这是一个从PHP转换而来的Go语言授权验证服务器，支持多设备并发连接。

## 功能特性

- 基于TCP Socket的服务器
- 支持多客户端并发连接（使用goroutine）
- AES-128-CBC加密通信
- MySQL数据库支持
- 授权码验证和设备绑定
- 自动过期时间管理

## 数据库配置

- 主机名: 127.0.0.1
- 端口: 3306
- 数据库名: qqqq
- 用户名: qqqq
- 密码: qqqq

## 数据库表结构

需要创建 `t_code` 表，包含以下字段：

```sql
CREATE TABLE `t_code` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `auth_code` varchar(255) NOT NULL COMMENT '授权码',
  `status` tinyint(1) DEFAULT '0' COMMENT '状态：0=未激活，1=已激活',
  `uuid` varchar(255) DEFAULT NULL COMMENT '设备唯一标识',
  `is_delete` tinyint(1) DEFAULT '0' COMMENT '删除标记：0=正常，1=已删除',
  `expiry_date` datetime DEFAULT NULL COMMENT '过期时间',
  `user_time` datetime DEFAULT NULL COMMENT '用户使用时间',
  `reg_time` varchar(20) DEFAULT NULL COMMENT '注册时间戳',
  `code_type` varchar(50) DEFAULT 'day' COMMENT '授权类型：day/month/year',
  PRIMARY KEY (`id`),
  UNIQUE KEY `auth_code` (`auth_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

## 安装和运行

1. 确保已安装Go 1.21或更高版本
2. 安装依赖：
   ```bash
   go mod tidy
   ```

### 方式一：直接运行（开发模式）
```bash
go run main.go
```

### 方式二：编译二进制文件（生产模式）
```bash
# 编译当前平台的二进制文件
go build -o auth-server main.go

# 运行编译后的二进制文件
./auth-server
```

### 方式三：交叉编译（不同平台）
```bash
# 编译Linux 64位版本
GOOS=linux GOARCH=amd64 go build -o auth-server-linux main.go

# 编译Windows 64位版本
GOOS=windows GOARCH=amd64 go build -o auth-server.exe main.go

# 编译macOS 64位版本
GOOS=darwin GOARCH=amd64 go build -o auth-server-mac main.go
```

服务器将在端口52669上监听连接。

## 通信协议

### 客户端请求格式
```json
{
  "code": "授权码",
  "seid": "设备唯一标识"
}
```

### 服务器响应格式
```json
{
  "code": "2000",  // 2000=成功，2002=失败
  "et": 1234567890,  // 过期时间戳（成功时）
  "ts": "1234567890"  // 当前时间戳（成功时）
}
```

## 加密说明

- 使用AES-128-CBC加密
- 密钥: p4m4gjk59o7nuedr
- IV: cf37z0j92mozpqft
- 数据经过Base64编码和自定义字符替换

## 并发支持

- 每个客户端连接都在独立的goroutine中处理
- 使用数据库连接池支持并发数据库操作
- 支持同时处理多个设备的授权请求

## 错误处理

- 完整的网络错误处理
- 数据库连接错误处理
- 加密解密错误处理
- 客户端超时处理（10秒）
